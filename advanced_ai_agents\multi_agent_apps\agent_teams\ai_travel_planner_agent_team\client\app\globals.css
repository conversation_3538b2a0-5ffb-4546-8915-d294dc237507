@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: D<PERSON>, sans-serif;
  --font-mono: Space Mono, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --radius: 0px;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-destructive-foreground: var(--destructive-foreground);
}

:root {
  --radius: 0px;
  --background: oklch(1 0 0);
  --foreground: oklch(0 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.6489 0.237 26.9728);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.968 0.211 109.7692);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.9551 0 0);
  --muted-foreground: oklch(0.3211 0 0);
  --accent: oklch(0.5635 0.2408 260.8178);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0 0 0);
  --border: oklch(0 0 0);
  --input: oklch(0 0 0);
  --ring: oklch(0.6489 0.237 26.9728);
  --chart-1: oklch(0.6489 0.237 26.9728);
  --chart-2: oklch(0.968 0.211 109.7692);
  --chart-3: oklch(0.5635 0.2408 260.8178);
  --chart-4: oklch(0.7323 0.2492 142.4953);
  --chart-5: oklch(0.5931 0.2726 328.3634);
  --sidebar: oklch(0.9551 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0.6489 0.237 26.9728);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.5635 0.2408 260.8178);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0 0 0);
  --sidebar-ring: oklch(0.6489 0.237 26.9728);
  --destructive-foreground: oklch(1 0 0);
  --font-sans: DM Sans, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Space Mono, monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 1;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 4px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1), 4px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 2px 4px -1px hsl(0 0% 0% / 1);
  --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 4px 6px -1px hsl(0 0% 0% / 1);
  --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 8px 10px -1px hsl(0 0% 0% / 1);
  --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.5);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0 0 0);
  --foreground: oklch(1 0 0);
  --card: oklch(0.3211 0 0);
  --card-foreground: oklch(1 0 0);
  --popover: oklch(0.3211 0 0);
  --popover-foreground: oklch(1 0 0);
  --primary: oklch(0.7044 0.1872 23.1858);
  --primary-foreground: oklch(0 0 0);
  --secondary: oklch(0.9691 0.2005 109.6228);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.3211 0 0);
  --muted-foreground: oklch(0.8452 0 0);
  --accent: oklch(0.6755 0.1765 252.2592);
  --accent-foreground: oklch(0 0 0);
  --destructive: oklch(1 0 0);
  --border: oklch(1 0 0);
  --input: oklch(1 0 0);
  --ring: oklch(0.7044 0.1872 23.1858);
  --chart-1: oklch(0.7044 0.1872 23.1858);
  --chart-2: oklch(0.9691 0.2005 109.6228);
  --chart-3: oklch(0.6755 0.1765 252.2592);
  --chart-4: oklch(0.7395 0.2268 142.8504);
  --chart-5: oklch(0.6131 0.2458 328.0714);
  --sidebar: oklch(0 0 0);
  --sidebar-foreground: oklch(1 0 0);
  --sidebar-primary: oklch(0.7044 0.1872 23.1858);
  --sidebar-primary-foreground: oklch(0 0 0);
  --sidebar-accent: oklch(0.6755 0.1765 252.2592);
  --sidebar-accent-foreground: oklch(0 0 0);
  --sidebar-border: oklch(1 0 0);
  --sidebar-ring: oklch(0.7044 0.1872 23.1858);
  --destructive-foreground: oklch(0 0 0);
  --radius: 0px;
  --font-sans: DM Sans, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Space Mono, monospace;
  --shadow-color: hsl(0 0% 0%);
  --shadow-opacity: 1;
  --shadow-blur: 0px;
  --shadow-spread: 0px;
  --shadow-offset-x: 4px;
  --shadow-offset-y: 4px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.5);
  --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1), 4px 1px 2px -1px hsl(0 0% 0% / 1);
  --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 2px 4px -1px hsl(0 0% 0% / 1);
  --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 4px 6px -1px hsl(0 0% 0% / 1);
  --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1),
    4px 8px 10px -1px hsl(0 0% 0% / 1);
  --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.5);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal);
  }
}
